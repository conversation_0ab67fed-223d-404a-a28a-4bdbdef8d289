import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";

const Login = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    role: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!formData.username || !formData.password || !formData.role) {
      setError("Please fill in all fields including role selection");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Simulate authentication
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock authentication - accept demo credentials
      if ((formData.username === "demo" || formData.username === "admin") &&
          (formData.password === "password" || formData.password === "demo")) {

        // Store auth info
        localStorage.setItem('userRole', formData.role);
        localStorage.setItem('username', formData.username);

        // Navigate based on role
        switch (formData.role) {
          case "admin":
            navigate("/admin");
            break;
          case "doctor":
            navigate("/doctor");
            break;
          case "nurse":
            navigate("/nurse");
            break;
          default:
            setError("Invalid role selected");
            setIsLoading(false);
            return;
        }
      } else {
        setError("Invalid credentials. Use demo/password or admin/demo");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Back to Home */}
        <div className="text-center mb-6">
          <Link
            to="/"
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            ← Back to Home
          </Link>
        </div>

        {/* Login Card */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="text-6xl mb-4">🏥</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">HospiTrack Login</h2>
            <p className="text-gray-600">Access your hospital management dashboard</p>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Your Role
              </label>
              <select
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isLoading}
              >
                <option value="">Choose your role...</option>
                <option value="admin">🧑‍💼 Hospital Administrator</option>
                <option value="doctor">👨‍⚕️ Medical Professional</option>
                <option value="nurse">👩‍⚕️ Nursing Staff</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Username
              </label>
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="Enter your username"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isLoading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isLoading}
              />
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                isLoading
                  ? 'bg-gray-400 cursor-not-allowed text-white'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </span>
              ) : (
                'Login to HospiTrack'
              )}
            </button>
          </form>

          {/* Demo Credentials */}
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="text-sm font-semibold text-green-800 mb-2">🔑 Demo Credentials:</h4>
            <div className="space-y-1 text-xs text-green-700">
              <p><strong>Option 1:</strong> Username: <span className="font-mono bg-white px-1 rounded">demo</span> | Password: <span className="font-mono bg-white px-1 rounded">password</span></p>
              <p><strong>Option 2:</strong> Username: <span className="font-mono bg-white px-1 rounded">admin</span> | Password: <span className="font-mono bg-white px-1 rounded">demo</span></p>
            </div>
            <p className="text-xs text-green-600 mt-2">
              ✅ Select any role above and use either set of credentials to login
            </p>
          </div>
        </div>

        {/* Quick Role Info */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600 mb-3">Available Roles:</p>
          <div className="grid grid-cols-3 gap-2">
            <div className="text-center p-2 bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="text-lg mb-1">🧑‍💼</div>
              <div className="text-xs font-medium text-gray-700">Administrator</div>
            </div>
            <div className="text-center p-2 bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="text-lg mb-1">👨‍⚕️</div>
              <div className="text-xs font-medium text-gray-700">Doctor</div>
            </div>
            <div className="text-center p-2 bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="text-lg mb-1">👩‍⚕️</div>
              <div className="text-xs font-medium text-gray-700">Nurse</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;