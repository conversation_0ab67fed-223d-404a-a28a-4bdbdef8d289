{"name": "hospit<PERSON>", "version": "1.0.0", "description": "HospiTrack Application", "main": "index.js", "scripts": {"dev": "cd frontend && npm run dev", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Nobody-08/HospiTrack.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Nobody-08/HospiTrack/issues"}, "homepage": "https://github.com/Nobody-08/HospiTrack#readme", "dependencies": {"axios": "^1.9.0", "vite": "^7.0.5"}}