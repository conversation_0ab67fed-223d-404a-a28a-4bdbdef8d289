# engine/__init__.py
# Copyright (C) 2005-2024 the SQLAlchemy authors and contributors
# <see AUTHORS file>
#
# This module is part of SQLAlchemy and is released under
# the MIT License: https://www.opensource.org/licenses/mit-license.php

"""SQL connections, SQL execution and high-level DB-API interface.

The engine package defines the basic components used to interface
DB-API modules with higher-level statement construction,
connection-management, execution and result contexts.  The primary
"entry point" class into this package is the Engine and its public
constructor ``create_engine()``.

"""

from . import events
from . import util
from .base import Connection
from .base import Engine
from .base import NestedTransaction
from .base import RootTransaction
from .base import Transaction
from .base import TwoPhaseTransaction
from .create import create_engine
from .create import engine_from_config
from .cursor import BaseCursorResult
from .cursor import BufferedColumnResultProxy
from .cursor import BufferedColumnRow
from .cursor import BufferedRowResultProxy
from .cursor import CursorResult
from .cursor import FullyBufferedResultProxy
from .cursor import LegacyCursorResult
from .cursor import ResultProxy
from .interfaces import AdaptedConnection
from .interfaces import Compiled
from .interfaces import Connectable
from .interfaces import CreateEnginePlugin
from .interfaces import Dialect
from .interfaces import ExceptionContext
from .interfaces import ExecutionContext
from .interfaces import TypeCompiler
from .mock import create_mock_engine
from .reflection import Inspector
from .result import ChunkedIteratorResult
from .result import FilterResult
from .result import FrozenResult
from .result import IteratorResult
from .result import MappingResult
from .result import MergedResult
from .result import Result
from .result import result_tuple
from .result import ScalarResult
from .row import BaseRow
from .row import LegacyRow
from .row import Row
from .row import RowMapping
from .url import make_url
from .url import URL
from .util import connection_memoize
from ..sql import ddl
