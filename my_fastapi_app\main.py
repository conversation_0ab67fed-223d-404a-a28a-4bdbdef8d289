# my_fastapi_app/main.py

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from auth import router as auth_router

app = FastAPI()
app.include_router(auth_router, prefix="/auth", tags=["auth"])

# Define the list of origins that are allowed to make requests
origins = [
    "http://localhost:3000", # Your React app
    "http://localhost:5173",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"], # Allow all methods
    allow_headers=["*"], # Allow all headers
)

@app.get("/api/dashboard/metrics/")
def get_metrics():
    return {"data": "some high-speed data"}
