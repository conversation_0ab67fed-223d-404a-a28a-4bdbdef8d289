# my_fastapi_app/main.py

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from .auth import router as auth_router

app = FastAPI()

# Include routers with /api prefix to match frontend expectations
app.include_router(auth_router, prefix="/api/auth", tags=["auth"])

# Define the list of origins that are allowed to make requests
origins = [
    "http://localhost:3000",  # React app
    "http://localhost:5173",  # Vite dev server
    "http://localhost:5174",  # Alternative Vite port
    "http://127.0.0.1:5173",
    "http://127.0.0.1:5174",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Dashboard endpoints
@app.get("/api/dashboard/system-stats/")
def get_system_stats():
    return {
        "total_beds": 150,
        "occupied_beds": 120,
        "available_beds": 30,
        "total_patients": 120,
        "total_staff": 45,
        "active_alerts": 3
    }

@app.get("/api/dashboard/bed-occupancy/")
def get_bed_occupancy():
    return {
        "icu": {"total": 20, "occupied": 18, "available": 2},
        "general": {"total": 100, "occupied": 85, "available": 15},
        "emergency": {"total": 30, "occupied": 17, "available": 13}
    }

@app.get("/api/dashboard/patient-stats/")
def get_patient_stats():
    return {
        "admitted_today": 8,
        "discharged_today": 5,
        "critical_patients": 12,
        "stable_patients": 108
    }

@app.get("/api/dashboard/alert-stats/")
def get_alert_stats():
    return {
        "critical": 1,
        "high": 2,
        "medium": 5,
        "low": 3
    }

@app.get("/api/dashboard/recent-activity/")
def get_recent_activity():
    return [
        {"time": "10:30 AM", "activity": "Patient John Doe admitted to ICU", "type": "admission"},
        {"time": "10:15 AM", "activity": "Emergency alert resolved in Ward 3", "type": "alert"},
        {"time": "09:45 AM", "activity": "Dr. Smith completed rounds", "type": "rounds"},
        {"time": "09:30 AM", "activity": "Bed 205 cleaned and ready", "type": "maintenance"}
    ]

# Patients endpoints
@app.get("/api/patients/")
def get_patients():
    return [
        {
            "id": 1,
            "name": "John Doe",
            "age": 45,
            "ward": "ICU",
            "bed_number": "101",
            "condition": "Critical",
            "admitted_date": "2025-01-15"
        },
        {
            "id": 2,
            "name": "Jane Smith",
            "age": 32,
            "ward": "General",
            "bed_number": "205",
            "condition": "Stable",
            "admitted_date": "2025-01-16"
        }
    ]

@app.post("/api/patients/")
def create_patient(patient_data: dict):
    return {"message": "Patient created successfully", "id": 123}

# Beds endpoints
@app.get("/api/beds/")
def get_beds():
    return [
        {
            "id": 1,
            "number": "101",
            "ward": "ICU",
            "status": "occupied",
            "patient_name": "John Doe",
            "last_cleaned": "2025-01-19 08:00"
        },
        {
            "id": 2,
            "number": "102",
            "ward": "ICU",
            "status": "available",
            "patient_name": None,
            "last_cleaned": "2025-01-19 09:00"
        },
        {
            "id": 3,
            "number": "205",
            "ward": "General",
            "status": "occupied",
            "patient_name": "Jane Smith",
            "last_cleaned": "2025-01-19 07:30"
        }
    ]

# Alerts endpoints
@app.get("/api/alerts/")
def get_alerts():
    return [
        {
            "id": 1,
            "title": "Equipment Malfunction",
            "description": "Ventilator in ICU Room 101 needs immediate attention",
            "severity": "critical",
            "ward": "ICU",
            "created_at": "2025-01-19 10:30",
            "resolved": False
        },
        {
            "id": 2,
            "title": "Low Oxygen Supply",
            "description": "Oxygen levels running low in Ward 3",
            "severity": "high",
            "ward": "General",
            "created_at": "2025-01-19 09:15",
            "resolved": False
        }
    ]

@app.post("/api/alerts/")
def create_alert(alert_data: dict):
    return {"message": "Alert created successfully", "id": 456}

# Staff endpoints
@app.get("/api/staff/")
def get_staff():
    return [
        {
            "id": 1,
            "name": "Dr. Smith",
            "role": "doctor",
            "department": "Cardiology",
            "on_duty": True,
            "shift": "Morning"
        },
        {
            "id": 2,
            "name": "Nurse Johnson",
            "role": "nurse",
            "department": "ICU",
            "on_duty": True,
            "shift": "Night"
        }
    ]

# OPD endpoints
@app.get("/api/opd/schedule/")
def get_opd_schedule():
    return [
        {
            "id": 1,
            "doctor_name": "Dr. Smith",
            "department": "Cardiology",
            "time_slot": "09:00-12:00",
            "available_slots": 5
        },
        {
            "id": 2,
            "doctor_name": "Dr. Johnson",
            "department": "Neurology",
            "time_slot": "14:00-17:00",
            "available_slots": 3
        }
    ]

@app.get("/api/opd/appointments/")
def get_appointments():
    return [
        {
            "id": 1,
            "patient_name": "Alice Brown",
            "doctor": "Dr. Smith",
            "time": "10:00 AM",
            "status": "confirmed"
        }
    ]

# Transfers endpoints
@app.get("/api/transfers/")
def get_transfers():
    return [
        {
            "id": 1,
            "patient_name": "John Doe",
            "from_ward": "General",
            "to_ward": "ICU",
            "status": "pending",
            "requested_by": "Dr. Smith",
            "requested_at": "2025-01-19 11:00"
        }
    ]

@app.get("/")
def root():
    return {"message": "HospiTrack API is running"}
