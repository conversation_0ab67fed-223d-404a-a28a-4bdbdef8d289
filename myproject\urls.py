"""
URL configuration for HospiTrack project.
"""
from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse

def root_view(request):
    return JsonResponse({
        "message": "HospiTrack Django Backend is running!",
        "api_endpoints": {
            "api_root": "/api/",
            "health": "/api/health/",
            "login": "/api/auth/login/"
        },
        "admin": "/admin/",
        "status": "Server is working correctly"
    })

urlpatterns = [
    path('', root_view, name='root'),  # Root endpoint
    path('admin/', admin.site.urls),
    path('api/', include('api.urls')),  # Include API URLs
]
