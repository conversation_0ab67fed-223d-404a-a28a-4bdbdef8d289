"""
URL configuration for HospiTrack project.
"""
from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
import json

def root_view(request):
    print(f"Root view called: {request.method} {request.path}")
    return JsonResponse({
        "message": "HospiTrack Django Backend is running!",
        "method": request.method,
        "path": request.path,
        "status": "success"
    })

def simple_test(request):
    print(f"Simple test called: {request.method} {request.path}")
    return HttpResponse("Simple test working!")

@csrf_exempt
def api_health(request):
    print(f"Health check called: {request.method} {request.path}")
    return JsonResponse({
        "status": "healthy",
        "message": "API is working",
        "method": request.method
    })

@csrf_exempt
def api_login(request):
    print(f"Login called: {request.method} {request.path}")
    if request.method == 'POST':
        try:
            data = json.loads(request.body) if request.body else {}
            email = data.get('email', '')
            password = data.get('password', '')

            print(f"Login attempt: {email}")

            # Mock authentication
            if email and password:
                return JsonResponse({
                    "access": "mock_jwt_token",
                    "refresh": "mock_refresh_token",
                    "user": {
                        "id": 1,
                        "name": "Test User",
                        "email": email,
                        "role": "admin"
                    }
                })
            else:
                return JsonResponse({"error": "Email and password required"}, status=400)
        except Exception as e:
            print(f"Login error: {e}")
            return JsonResponse({"error": str(e)}, status=500)

    return JsonResponse({"error": "POST method required"}, status=405)

urlpatterns = [
    path('', root_view, name='root'),
    path('test/', simple_test, name='simple_test'),
    path('api/health/', api_health, name='api_health'),
    path('api/auth/login/', api_login, name='api_login'),
    path('admin/', admin.site.urls),
]
