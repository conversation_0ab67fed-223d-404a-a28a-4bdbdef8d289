#!/usr/bin/env python3
"""
Direct FastAPI Server for HospiTrack with Database
"""
import uvicorn
import os
import subprocess
import sys

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Packages installed successfully!")
    except subprocess.CalledProcessError:
        print("❌ Failed to install packages. Please run: pip install -r requirements.txt")
        return False
    return True

if __name__ == "__main__":
    print("🏥 Starting HospiTrack FastAPI Backend with Database...")

    # Install requirements
    if not install_requirements():
        sys.exit(1)

    # Setup database
    print("🗄️ Setting up database...")
    try:
        subprocess.check_call([sys.executable, "setup_database.py"])
    except subprocess.CalledProcessError:
        print("❌ Database setup failed")
        sys.exit(1)

    print("📍 Server will run on http://localhost:8000")
    print("🌐 CORS is configured for http://localhost:5174")
    print("🔗 API Documentation: http://localhost:8000/docs")
    print("🔗 Health Check: http://localhost:8000/api/health/")
    print("🔗 Login Endpoint: http://localhost:8000/api/auth/login/")
    print()
    print("🔑 Default login credentials:")
    print("   Admin: <EMAIL> / admin123")
    print("   Doctor: <EMAIL> / doctor123")
    print("   Nurse: <EMAIL> / nurse123")
    print()

    # Change to the my_fastapi_app directory
    os.chdir("my_fastapi_app")

    # Run the FastAPI app directly
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
