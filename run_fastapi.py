#!/usr/bin/env python3
"""
Direct FastAPI Server for HospiTrack (No Import Issues)
"""
import uvicorn
import os

if __name__ == "__main__":
    print("🏥 Starting HospiTrack FastAPI Backend...")
    print("📍 Server will run on http://localhost:8000")
    print("🌐 CORS is configured for http://localhost:5174")
    print("🔗 API Documentation: http://localhost:8000/docs")
    print("🔗 Health Check: http://localhost:8000/api/health/")
    print("🔗 Login Endpoint: http://localhost:8000/api/auth/login/")
    print()
    
    # Change to the my_fastapi_app directory
    os.chdir("my_fastapi_app")
    
    # Run the FastAPI app directly
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
