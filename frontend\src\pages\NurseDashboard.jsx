import { useState } from "react";
import DashboardCard from "../components/DashboardCard";

const NurseDashboard = () => {
  // Example state for beds and patients
  const [beds, setBeds] = useState([
    { id: 1, status: "Available" },
    { id: 2, status: "Occupied" },
    { id: 3, status: "Cleaning" },
    { id: 4, status: "Maintenance" },
  ]);
  const [patients, setPatients] = useState([
    { id: 1, name: "<PERSON>", status: "Stable", bed: 2 },
    { id: 2, name: "<PERSON>", status: "Critical", bed: 3 },
  ]);
  const [criticalCases, setCriticalCases] = useState([]);

  // Placeholder handlers
  const handleTransfer = (patientId, newBedId) => {
    // Implement transfer logic
    alert(`Transferred patient ${patientId} to bed ${newBedId}`);
  };
  const handleUpdateBedStatus = (bedId, status) => {
    setBeds(beds.map(b => b.id === bedId ? { ...b, status } : b));
  };
  const handleEscalate = (patientId) => {
    // Implement escalation logic
    alert(`Escalated patient ${patientId} to doctor`);
  };
  const handleUpdateStatusWithDoctor = (patientId, status) => {
    // Implement update logic
    alert(`Updated doctor with status for patient ${patientId}: ${status}`);
  };

  // Calculate bed stats for cards
  const bedStats = {
    total: beds.length,
    available: beds.filter(b => b.status === "Available").length,
    occupied: beds.filter(b => b.status === "Occupied").length,
    cleaning: beds.filter(b => b.status === "Cleaning").length,
    maintenance: beds.filter(b => b.status === "Maintenance").length,
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <h1 className="text-3xl font-bold mb-8">🧑‍⚕️ Nurse Dashboard</h1>
      {/* Bed Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-10">
        <DashboardCard
          title="Total Beds"
          value={bedStats.total}
          icon="🛏️"
          color="blue"
          subtitle="All hospital beds"
        />
        <DashboardCard
          title="Available"
          value={bedStats.available}
          icon="✅"
          color="green"
          subtitle="Ready for patients"
        />
        <DashboardCard
          title="Occupied"
          value={bedStats.occupied}
          icon="👤"
          color="gray"
          subtitle="Currently in use"
        />
        <DashboardCard
          title="Cleaning"
          value={bedStats.cleaning}
          icon="🧹"
          color="yellow"
          subtitle="Being sanitized"
        />
        <DashboardCard
          title="Maintenance"
          value={bedStats.maintenance}
          icon="🛠️"
          color="purple"
          subtitle="Under maintenance"
        />
      </div>

      {/* Patient Transfer & Escalation */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold mb-4">Patient Management</h2>
        <ul>
          {patients.map(patient => (
            <li key={patient.id} className="mb-4">
              <div className="flex justify-between items-center">
                <span>{patient.name} (Bed {patient.bed}) - {patient.status}</span>
                <button className="ml-2 text-blue-600 underline" onClick={() => handleEscalate(patient.id)}>Escalate</button>
              </div>
              <div className="flex items-center mt-2 space-x-2">
                <label>Transfer to bed:</label>
                <select onChange={e => handleTransfer(patient.id, e.target.value)} defaultValue="">
                  <option value="" disabled>Select bed</option>
                  {beds.filter(b => b.status === "Available").map(bed => (
                    <option key={bed.id} value={bed.id}>Bed {bed.id}</option>
                  ))}
                </select>
                <button className="ml-2 text-green-600 underline" onClick={() => handleUpdateStatusWithDoctor(patient.id, 'Updated')}>Update Doctor</button>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default NurseDashboard;
