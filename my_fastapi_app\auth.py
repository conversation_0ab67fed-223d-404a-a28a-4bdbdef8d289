from fastapi import <PERSON>Router, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional

router = APIRouter()

# In-memory data store for all users
users_db: Dict[str, "User"] = {}

class User(BaseModel):
    name: str
    email: str
    password: str
    role: str

class LoginRequest(BaseModel):
    email: str
    password: str

class RegisterRequest(BaseModel):
    name: str
    email: str
    password: str
    role: Optional[str] = None

# Authentication endpoints matching frontend expectations
@router.post("/login/")
async def login(login_request: LoginRequest):
    user = users_db.get(login_request.email)
    if not user or user.password != login_request.password:
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    return {
        "message": "Login successful", 
        "role": user.role,
        "access_token": f"fake_token_{user.email}",
        "user": {
            "name": user.name,
            "email": user.email,
            "role": user.role
        }
    }

@router.post("/logout/")
async def logout():
    return {"message": "Logout successful"}

@router.post("/refresh/")
async def refresh_token(refresh_data: dict):
    return {"access_token": "new_fake_token", "message": "Token refreshed"}

# Role-specific registration endpoints
@router.post("/admin/register/")
async def register_admin(user_data: RegisterRequest):
    if user_data.email in users_db:
        raise HTTPException(status_code=400, detail="User with this email already exists")
    
    user = User(name=user_data.name, email=user_data.email, password=user_data.password, role="admin")
    users_db[user_data.email] = user
    return {"message": "Admin registered successfully"}

@router.post("/doctor/register/")
async def register_doctor(user_data: RegisterRequest):
    if user_data.email in users_db:
        raise HTTPException(status_code=400, detail="User with this email already exists")
    
    user = User(name=user_data.name, email=user_data.email, password=user_data.password, role="doctor")
    users_db[user_data.email] = user
    return {"message": "Doctor registered successfully"}

@router.post("/nurse/register/")
async def register_nurse(user_data: RegisterRequest):
    if user_data.email in users_db:
        raise HTTPException(status_code=400, detail="User with this email already exists")
    
    user = User(name=user_data.name, email=user_data.email, password=user_data.password, role="nurse")
    users_db[user_data.email] = user
    return {"message": "Nurse registered successfully"}

# Profile endpoints
@router.get("/admin/profile/")
async def get_admin_profile():
    return {
        "name": "Admin User",
        "email": "<EMAIL>",
        "role": "admin",
        "department": "Administration"
    }

@router.get("/doctor/profile/")
async def get_doctor_profile():
    return {
        "name": "Dr. Smith",
        "email": "<EMAIL>",
        "role": "doctor",
        "department": "Cardiology",
        "specialization": "Heart Surgery"
    }

@router.get("/nurse/profile/")
async def get_nurse_profile():
    return {
        "name": "Nurse Johnson",
        "email": "<EMAIL>",
        "role": "nurse",
        "department": "ICU",
        "shift": "Night"
    }

# Admin endpoints for managing users
@router.get("/admin/doctors", response_model=List[User])
async def get_doctors():
    return [user for user in users_db.values() if user.role == 'doctor']

@router.get("/admin/nurses", response_model=List[User])
async def get_nurses():
    return [user for user in users_db.values() if user.role == 'nurse']

@router.get("/admin/all_users", response_model=List[User])
async def get_all_users():
    return list(users_db.values())

# Legacy signup endpoint for backward compatibility
@router.post("/signup")
async def signup(user: User):
    if user.email in users_db:
        raise HTTPException(status_code=400, detail="User with this email already exists")
    
    if user.role not in ["doctor", "nurse", "admin"]:
        raise HTTPException(status_code=400, detail="Invalid role specified")

    users_db[user.email] = user
    return {"message": f"{user.role.capitalize()} created successfully"}
