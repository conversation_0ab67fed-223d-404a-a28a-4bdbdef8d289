from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Dict

router = APIRouter()

# In-memory data store for all users
users_db: Dict[str, "User"] = {}

class User(BaseModel):
    name: str
    email: str
    password: str
    role: str

class LoginRequest(BaseModel):
    email: str
    password: str

@router.post("/signup")
async def signup(user: User):
    if user.email in users_db:
        raise HTTPException(status_code=400, detail="User with this email already exists")
    
    if user.role not in ["doctor", "nurse", "admin"]:
        raise HTTPException(status_code=400, detail="Invalid role specified")

    users_db[user.email] = user
    return {"message": f"{user.role.capitalize()} created successfully"}

@router.post("/login")
async def login(login_request: LoginRequest):
    user = users_db.get(login_request.email)
    if not user or user.password != login_request.password:
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    return {"message": "Login successful", "role": user.role}

@router.get("/admin/doctors", response_model=List[User])
async def get_doctors():
    return [user for user in users_db.values() if user.role == 'doctor']

@router.get("/admin/nurses", response_model=List[User])
async def get_nurses():
    return [user for user in users_db.values() if user.role == 'nurse']

@router.get("/admin/all_users", response_model=List[User])
async def get_all_users():
    return list(users_db.values())
