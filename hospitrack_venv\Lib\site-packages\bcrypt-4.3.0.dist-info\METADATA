Metadata-Version: 2.1
Name: bcrypt
Version: 4.3.0
Summary: Modern password hashing for your software and your servers
Author-email: The Python Cryptographic Authority developers <<EMAIL>>
License: Apache-2.0
Project-URL: homepage, https://github.com/pyca/bcrypt/
Classifier: Development Status :: 5 - Production/Stable
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
Provides-Extra: tests
Requires-Dist: pytest!=3.3.0,>=3.2.1; extra == "tests"
Provides-Extra: typecheck
Requires-Dist: mypy; extra == "typecheck"

bcrypt
======

.. image:: https://img.shields.io/pypi/v/bcrypt.svg
    :target: https://pypi.org/project/bcrypt/
    :alt: Latest Version

.. image:: https://github.com/pyca/bcrypt/workflows/CI/badge.svg?branch=main
    :target: https://github.com/pyca/bcrypt/actions?query=workflow%3ACI+branch%3Amain

Acceptable password hashing for your software and your servers (but you should
really use argon2id or scrypt)


Installation
============

To install bcrypt, simply:

.. code:: console

    $ pip install bcrypt

Note that bcrypt should build very easily on Linux provided you have a C
compiler and a Rust compiler (the minimum supported Rust version is 1.56.0).

For Debian and Ubuntu, the following command will ensure that the required dependencies are installed:

.. code:: console

    $ sudo apt-get install build-essential cargo

For Fedora and RHEL-derivatives, the following command will ensure that the required dependencies are installed:

.. code:: console

    $ sudo yum install gcc cargo

For Alpine, the following command will ensure that the required dependencies are installed:

.. code:: console

    $ apk add --update musl-dev gcc cargo


Alternatives
============

While bcrypt remains an acceptable choice for password storage, depending on your specific use case you may also want to consider using scrypt (either via `standard library`_ or `cryptography`_) or argon2id via `argon2_cffi`_.

Changelog
=========

Unreleased
----------

* Dropped support for Python 3.7.
* We now support free-threaded Python 3.13.
* We now support PyPy 3.11.
* We now publish wheels for free-threaded Python 3.13, for PyPy 3.11 on
  ``manylinux``, and for ARMv7l on ``manylinux``.

4.2.1
-----

* Bump Rust dependency versions - this should resolve crashes on Python 3.13
  free-threaded builds.
* We no longer build ``manylinux`` wheels for PyPy 3.9.

4.2.0
-----

* Bump Rust dependency versions
* Removed the ``BCRYPT_ALLOW_RUST_163`` environment variable.

4.1.3
-----

* Bump Rust dependency versions

4.1.2
-----

* Publish both ``py37`` and ``py39`` wheels. This should resolve some errors
  relating to initializing a module multiple times per process.

4.1.1
-----

* Fixed the type signature on the ``kdf`` method.
* Fixed packaging bug on Windows.
* Fixed incompatibility with passlib package detection assumptions.

4.1.0
-----

* Dropped support for Python 3.6.
* Bumped MSRV to 1.64. (Note: Rust 1.63 can be used by setting the ``BCRYPT_ALLOW_RUST_163`` environment variable)

4.0.1
-----

* We now build PyPy ``manylinux`` wheels.
* Fixed a bug where passing an invalid ``salt`` to ``checkpw`` could result in
  a ``pyo3_runtime.PanicException``. It now correctly raises a ``ValueError``.

4.0.0
-----

* ``bcrypt`` is now implemented in Rust. Users building from source will need
  to have a Rust compiler available. Nothing will change for users downloading
  wheels.
* We no longer ship ``manylinux2010`` wheels. Users should upgrade to the latest
  ``pip`` to ensure this doesn’t cause issues downloading wheels on their
  platform. We now ship ``manylinux_2_28`` wheels for users on new enough platforms.
* ``NUL`` bytes are now allowed in inputs.


3.2.2
-----

* Fixed packaging of ``py.typed`` files in wheels so that ``mypy`` works.

3.2.1
-----

* Added support for compilation on z/OS
* The next release of ``bcrypt`` with be 4.0 and it will require Rust at
  compile time, for users building from source. There will be no additional
  requirement for users who are installing from wheels. Users on most
  platforms will be able to obtain a wheel by making sure they have an up to
  date ``pip``. The minimum supported Rust version will be 1.56.0.
* This will be the final release for which we ship ``manylinux2010`` wheels.
  Going forward the minimum supported manylinux ABI for our wheels will be
  ``manylinux2014``. The vast majority of users will continue to receive
  ``manylinux`` wheels provided they have an up to date ``pip``.


3.2.0
-----

* Added typehints for library functions.
* Dropped support for Python versions less than 3.6 (2.7, 3.4, 3.5).
* Shipped ``abi3`` Windows wheels (requires pip >= 20).

3.1.7
-----

* Set a ``setuptools`` lower bound for PEP517 wheel building.
* We no longer distribute 32-bit ``manylinux1`` wheels. Continuing to produce
  them was a maintenance burden.

3.1.6
-----

* Added support for compilation on Haiku.

3.1.5
-----

* Added support for compilation on AIX.
* Dropped Python 2.6 and 3.3 support.
* Switched to using ``abi3`` wheels for Python 3. If you are not getting a
  wheel on a compatible platform please upgrade your ``pip`` version.

3.1.4
-----

* Fixed compilation with mingw and on illumos.

3.1.3
-----
* Fixed a compilation issue on Solaris.
* Added a warning when using too few rounds with ``kdf``.

3.1.2
-----
* Fixed a compile issue affecting big endian platforms.
* Fixed invalid escape sequence warnings on Python 3.6.
* Fixed building in non-UTF8 environments on Python 2.

3.1.1
-----
* Resolved a ``UserWarning`` when used with ``cffi`` 1.8.3.

3.1.0
-----
* Added support for ``checkpw``, a convenience method for verifying a password.
* Ensure that you get a ``$2y$`` hash when you input a ``$2y$`` salt.
* Fixed a regression where ``$2a`` hashes were vulnerable to a wraparound bug.
* Fixed compilation under Alpine Linux.

3.0.0
-----
* Switched the C backend to code obtained from the OpenBSD project rather than
  openwall.
* Added support for ``bcrypt_pbkdf`` via the ``kdf`` function.

2.0.0
-----
* Added support for an adjustible prefix when calling ``gensalt``.
* Switched to CFFI 1.0+

Usage
-----

Password Hashing
~~~~~~~~~~~~~~~~

Hashing and then later checking that a password matches the previous hashed
password is very simple:

.. code:: pycon

    >>> import bcrypt
    >>> password = b"super secret password"
    >>> # Hash a password for the first time, with a randomly-generated salt
    >>> hashed = bcrypt.hashpw(password, bcrypt.gensalt())
    >>> # Check that an unhashed password matches one that has previously been
    >>> # hashed
    >>> if bcrypt.checkpw(password, hashed):
    ...     print("It Matches!")
    ... else:
    ...     print("It Does not Match :(")

KDF
~~~

As of 3.0.0 ``bcrypt`` now offers a ``kdf`` function which does ``bcrypt_pbkdf``.
This KDF is used in OpenSSH's newer encrypted private key format.

.. code:: pycon

    >>> import bcrypt
    >>> key = bcrypt.kdf(
    ...     password=b'password',
    ...     salt=b'salt',
    ...     desired_key_bytes=32,
    ...     rounds=100)


Adjustable Work Factor
~~~~~~~~~~~~~~~~~~~~~~
One of bcrypt's features is an adjustable logarithmic work factor. To adjust
the work factor merely pass the desired number of rounds to
``bcrypt.gensalt(rounds=12)`` which defaults to 12):

.. code:: pycon

    >>> import bcrypt
    >>> password = b"super secret password"
    >>> # Hash a password for the first time, with a certain number of rounds
    >>> hashed = bcrypt.hashpw(password, bcrypt.gensalt(14))
    >>> # Check that a unhashed password matches one that has previously been
    >>> #   hashed
    >>> if bcrypt.checkpw(password, hashed):
    ...     print("It Matches!")
    ... else:
    ...     print("It Does not Match :(")


Adjustable Prefix
~~~~~~~~~~~~~~~~~

Another one of bcrypt's features is an adjustable prefix to let you define what
libraries you'll remain compatible with. To adjust this, pass either ``2a`` or
``2b`` (the default) to ``bcrypt.gensalt(prefix=b"2b")`` as a bytes object.

As of 3.0.0 the ``$2y$`` prefix is still supported in ``hashpw`` but deprecated.

Maximum Password Length
~~~~~~~~~~~~~~~~~~~~~~~

The bcrypt algorithm only handles passwords up to 72 characters, any characters
beyond that are ignored. To work around this, a common approach is to hash a
password with a cryptographic hash (such as ``sha256``) and then base64
encode it to prevent NULL byte problems before hashing the result with
``bcrypt``:

.. code:: pycon

    >>> password = b"an incredibly long password" * 10
    >>> hashed = bcrypt.hashpw(
    ...     base64.b64encode(hashlib.sha256(password).digest()),
    ...     bcrypt.gensalt()
    ... )

Compatibility
-------------

This library should be compatible with py-bcrypt and it will run on Python
3.8+ (including free-threaded builds), and PyPy 3.

Security
--------

``bcrypt`` follows the `same security policy as cryptography`_, if you
identify a vulnerability, we ask you to contact us privately.

.. _`same security policy as cryptography`: https://cryptography.io/en/latest/security.html
.. _`standard library`: https://docs.python.org/3/library/hashlib.html#hashlib.scrypt
.. _`argon2_cffi`: https://argon2-cffi.readthedocs.io
.. _`cryptography`: https://cryptography.io/en/latest/hazmat/primitives/key-derivation-functions/#cryptography.hazmat.primitives.kdf.scrypt.Scrypt
