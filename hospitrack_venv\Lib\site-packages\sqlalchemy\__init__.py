# __init__.py
# Copyright (C) 2005-2024 the SQLAlchemy authors and contributors
# <see AUTHORS file>
#
# This module is part of SQLAlchemy and is released under
# the MIT License: https://www.opensource.org/licenses/mit-license.php

from . import util as _util
from .engine import create_engine
from .engine import create_mock_engine
from .engine import engine_from_config
from .inspection import inspect
from .schema import BLANK_SCHEMA
from .schema import CheckConstraint
from .schema import Column
from .schema import ColumnDefault
from .schema import Computed
from .schema import Constraint
from .schema import DDL
from .schema import DefaultClause
from .schema import FetchedValue
from .schema import ForeignKey
from .schema import ForeignKeyConstraint
from .schema import Identity
from .schema import Index
from .schema import MetaData
from .schema import PrimaryKeyConstraint
from .schema import Sequence
from .schema import Table
from .schema import ThreadLocalMetaData
from .schema import UniqueConstraint
from .sql import alias
from .sql import all_
from .sql import and_
from .sql import any_
from .sql import asc
from .sql import between
from .sql import bindparam
from .sql import case
from .sql import cast
from .sql import collate
from .sql import column
from .sql import delete
from .sql import desc
from .sql import distinct
from .sql import except_
from .sql import except_all
from .sql import exists
from .sql import extract
from .sql import false
from .sql import func
from .sql import funcfilter
from .sql import insert
from .sql import intersect
from .sql import intersect_all
from .sql import join
from .sql import LABEL_STYLE_DEFAULT
from .sql import LABEL_STYLE_DISAMBIGUATE_ONLY
from .sql import LABEL_STYLE_NONE
from .sql import LABEL_STYLE_TABLENAME_PLUS_COL
from .sql import lambda_stmt
from .sql import lateral
from .sql import literal
from .sql import literal_column
from .sql import modifier
from .sql import not_
from .sql import null
from .sql import nulls_first
from .sql import nulls_last
from .sql import nullsfirst
from .sql import nullslast
from .sql import or_
from .sql import outerjoin
from .sql import outparam
from .sql import over
from .sql import select
from .sql import subquery
from .sql import table
from .sql import tablesample
from .sql import text
from .sql import true
from .sql import tuple_
from .sql import type_coerce
from .sql import union
from .sql import union_all
from .sql import update
from .sql import values
from .sql import within_group
from .types import ARRAY
from .types import BIGINT
from .types import BigInteger
from .types import BINARY
from .types import BLOB
from .types import BOOLEAN
from .types import Boolean
from .types import CHAR
from .types import CLOB
from .types import DATE
from .types import Date
from .types import DATETIME
from .types import DateTime
from .types import DECIMAL
from .types import Enum
from .types import FLOAT
from .types import Float
from .types import INT
from .types import INTEGER
from .types import Integer
from .types import Interval
from .types import JSON
from .types import LargeBinary
from .types import NCHAR
from .types import NUMERIC
from .types import Numeric
from .types import NVARCHAR
from .types import PickleType
from .types import REAL
from .types import SMALLINT
from .types import SmallInteger
from .types import String
from .types import TEXT
from .types import Text
from .types import TIME
from .types import Time
from .types import TIMESTAMP
from .types import TupleType
from .types import TypeDecorator
from .types import Unicode
from .types import UnicodeText
from .types import VARBINARY
from .types import VARCHAR


__version__ = "1.4.53"


def __go(lcls):
    global __all__

    from . import events
    from . import util as _sa_util

    import inspect as _inspect

    __all__ = sorted(
        name
        for name, obj in lcls.items()
        if not (name.startswith("_") or _inspect.ismodule(obj))
    )

    _sa_util.preloaded.import_prefix("sqlalchemy")

    from . import exc

    exc._version_token = "".join(__version__.split(".")[0:2])


__go(locals())
