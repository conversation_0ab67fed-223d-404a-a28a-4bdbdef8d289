#!/usr/bin/env python3
"""
Database setup script for HospiTrack
"""
import sys
import os

# Add the my_fastapi_app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'my_fastapi_app'))

from database import create_tables, clear_all_data, SessionLocal
from auth_service import create_default_users

def setup_fresh_database():
    """Set up a fresh database with default users"""
    print("🏥 Setting up HospiTrack Database...")
    
    # Clear all existing data
    print("🗑️ Clearing existing data...")
    clear_all_data()
    
    # Create tables
    print("🗄️ Creating database tables...")
    create_tables()
    
    # Create default users
    print("👥 Creating default users...")
    db = SessionLocal()
    try:
        create_default_users(db)
    finally:
        db.close()
    
    print("✅ Database setup complete!")
    print()
    print("🔑 Default login credentials:")
    print("   Admin: <EMAIL> / admin123")
    print("   Doctor: <EMAIL> / doctor123")
    print("   Nurse: <EMAIL> / nurse123")

if __name__ == "__main__":
    setup_fresh_database()
